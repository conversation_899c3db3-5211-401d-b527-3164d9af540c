<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    align-center
    class="dialog-form"
    width="560px"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">音频验听标记</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="80px"
      ref="editRef"
      :disabled="isChecked"
    >
      <el-form-item label="验听标记：" prop="audioTag">
        <div class="tw-w-full">
          <el-input
            v-model.trim="addData.audioTag"
            :autosize="{minRows:3, maxRows:5}"
            type="textarea"
            show-word-limit
            :maxlength="30"
            placeholder="请输入音频验听标记"
          />
          <div class="tw-grid tw-grid-cols-6 tw-gap-[6px] tw-mt-1">
            <el-tag v-for="item in markShortcuts" class="tw-cursor-pointer" @click="addMark(item)" :key="item">
              {{ item }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, } from 'vue'
import { ElMessage, } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { trace } from '@/utils/trace'
import { markShortcuts } from './constant'

const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const scriptId = scriptStore.id
const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
type AudioItem = {
  id?: number,
  name?: string | null // 语料名称
  content?: string | null // 文字内容
  audioTag?: string | null // 音频标记
}
const props = defineProps<{
  corpusData: AudioItem;
  visible: boolean
}>();
const dialogVisible = ref<boolean>(false)
const addData = reactive<AudioItem>(props.corpusData)
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    Object.assign(addData, props.corpusData)
  }
})

const editRef = ref<FormInstance  | null>(null)

const rules = {
  audioTag: [
    { required: true, message: '请输入验听验听标记', trigger: ['blur', 'change'] },
  ],
}

const addMark = (item: string) => {
  addData.audioTag = (addData.audioTag || '') + item
}

// 底部确认、取消
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        id: addData.id!,
        audioTag: addData.audioTag || '',
      }
      trace({
        page: `话术编辑-音频管理-音频验听标记(${scriptId})`,
        params
      })
      const [err] = await to(scriptCorpusModel.saveAudioMark(params))
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm', addData.audioTag)
        cancel()
      }
      loading.value = false
    }
  })
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: 14px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 16px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
