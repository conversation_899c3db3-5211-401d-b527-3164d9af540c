<template>
  <el-scrollbar v-if="rowDialogData && rowDialogData?.length>0" ref="infiniteScrollbarRef" class="tw-w-full tw-bg-[#f5f5f5]" wrap-class="tw-pt-[16px] tw-pl-[32px] tw-pr-[24px] tw-pb-[8px]">
    <div class="tw-absolute tw-right-1 tw-top-1 tw-z-[9]">
       <el-button v-if="!!corpusTriggerData?.triggerSmsList?.length" v-loading="loading" type="primary" @click="goTriggerCorpus(1)">
        <el-icon :size="15"><SvgIcon name="chevrons-down"/></el-icon>
        <span>发短信</span>
      </el-button>
      <el-button v-if="!!corpusTriggerData?.tirggerHumanList?.length" v-loading="loading" type="primary" @click="goTriggerCorpus(2)">
        <el-icon :size="15"><SvgIcon name="chevrons-down"/></el-icon>
        <span>转人工</span>
      </el-button>
      <el-button v-if="!!props.callRecordData?.isTransToCallSeat" v-loading="loading" type="primary" @click="goPopWinTime">
        <el-icon :size="15"><SvgIcon name="chevrons-down"/></el-icon>
        <span>转人工弹窗</span>
      </el-button>
    </div>
    <div v-infinite-scroll="handleInfiniteScroll" :infinite-scroll-immediate="false" :infinite-scroll-distance="1" class="tw-flex tw-flex-col tw-pb-[20px]">
      <template v-for="(itemData) in rowDialogData.slice(0, count)">
        <!-- 时间-操作 显示模块，注：沉默也不会有id，通过是否有dialogTime进行过滤 -->
        <div v-if="typeof itemData.id !== 'number' && itemData.dialogTime" :class="getTimeClass(itemData.content)" class="time-box"> {{ dayjs(itemData.dialogTime).format('YYYY-MM-DD HH:mm:ss') + ' ' + itemData.content }}</div>

        <!-- 用户(type: 1,2)对话模块 -->
        <div v-else-if="itemData.content?.includes('用户沉默中') || (itemData.type && [1, 2].includes(itemData.type))" class="tw-mb-3 tw-flex">
          <!-- 通过content包含【用户沉默中】，判断是否为沉默（沉默语料、沉默分支、查询分支沉默） -->
          <!-- 用户对话头像 -->
          <span v-if="itemData.content?.includes('用户沉默中')" class="dialog-o grey-o">默</span>
          <span v-else-if="!itemData.content" class="dialog-o grey-o">空</span>
          <span v-else class="dialog-o">{{  props.callRecordData?.name ? props.callRecordData.name?.[0] || '客' : '客' }}</span>

          <span class="tw-ml-1">
            <div class="tw-m-0.5 tw-text-left">
              <!-- 用户对话气泡标题：沉默则用content，否则为用户回复 -->
              <span
                class="tw-rounded-[4px] tw-mr-1 tw-p-0.5"
                :class="itemData.content?.includes('用户沉默中') ? 'tw-bg-[#eaf0fe] tw-text-[var(--el-color-primary)]' : 'tw-bg-[#E6E6E6] tw-text-[#323233]'"
              >
                {{ itemData.content?.includes('用户沉默中') ? itemData.content?.replace('用户', '客户') : '客户回复' }}
              </span>
              <span>{{ itemData.dialogTime || '' }}</span>
            </div>
            <!-- 用户对话气泡内容：沉默不显示 -->
            <div
              v-if="itemData.urls?.length"
              class="box-normal box-left box-dark"
              @click="handleAudioPlay(itemData)"
            >
              <template v-if="!itemData.content?.includes('用户沉默中')">
                <el-icon v-if="currentVoice[0] === itemData.id" :size="14" class="tw-absolute tw-top-[2px] tw-left-[2px]"><SvgIcon name="voice-playing" color="#fff"></SvgIcon></el-icon>
                <el-icon v-else :size="14"><SvgIcon name="voice" color="#fff" class="tw-absolute tw-top-[2px] tw-left-[2px]"></SvgIcon></el-icon>
                <span class="tw-mr-1 tw-pl-[6px]">{{ (Math.round((itemData.speakMs||0)/1000) || '1') + '”' }}</span>
              </template>
              <span class="tw-leading-[22px] tw-break-all" v-html="formatContent(itemData, 'blue')" />
            </div>
            <!-- 用户对话气泡底部信息（未命中）：显示未命中 -->
            <!-- <div v-if="itemData.hitBranch == '未命中'" class="tw-text-left tw-text-[--el-text-color-primary] tw-text-[12px]" :class="props.train ? 'tw-pr-0.5' : 'tw-pl-0.5'">未命中</div> -->
            <!-- 用户对话气泡底部信息（非未命中）：命中短语、命中意图、命中事件（仅查询分支、沉默显示）、触发 -->
            <div v-if="!(!itemData.hitSemantic && !itemData.hitPhrase && !itemData.hitBranch)" class="tw-bg-white tw-rounded-[4px] tw-mt-[8px] tw-p-[8px] tw-flex tw-flex-col tw-items-start">
              <div v-if="itemData.hitPhrase"><span class="label">命中短语：</span><span>{{ itemData.hitPhrase || '' }}</span></div>
              <div v-if="itemData.hitSemantic">
                <span class="label">命中语义：</span>
                <span
                  v-for="(s,i) in itemData.hitSemantic?.split(',') || []"
                  :key="s"
                >
                  <span>{{ i > 0 ? '、' : '' }}</span>
                  <span :class="translateHitSemanticFromBranch(itemData.hitBranch || '') === s ? 'tw-text-[var(--el-color-primary)] tw-font-[600]' : ''">{{s || ''}}</span>
                </span>
              </div>
              <div v-if="itemData.hitBranch?.includes('沉默') || itemData.hitBranch?.includes('查询分支')">
                <span class="label">命中事件：</span>
                <div class="tw-inline-flex tw-flex-col tw-break-all tw-text-left">
                  <span v-for="v in translateBranchType(itemData.hitBranch, infoQueryMap)" :key="v" class="tw-flex-shrink">{{ v }}</span>
                </div>
              </div>
              <div v-if="itemData.hitBranch">
                <span class="label">触发：</span>
                <span class="tw-break-all tw-text-left">
                  {{ translateBranchMsg(itemData.hitBranch) || '' }}
                </span>
              </div>
            </div>
          </span>
        </div>

        <!-- 机器人(type: 0)\坐席(type: 3, 4)对话模块 -->
        <div v-else class="tw-mb-3 tw-flex tw-justify-end">
          <span class="tw-flex tw-flex-col tw-mr-1">
            <!-- 机器人(type: 0)\坐席(type: 3, 4)气泡标题：语料名称：时间 -->
            <div class="tw-flex tw-items-center tw-h-[16px] tw-m-0.5">
              <span v-if="itemData?.corpusName" class="tw-bg-[#E6E6E6] tw-text-[#323233] tw-rounded-[4px] tw-p-0.5 tw-mr-1">{{ itemData.corpusName || '' }}</span>
              <span>{{ itemData?.dialogTime || '' }}</span>
              <el-icon
                v-if="itemData?.corpusName && corpusTriggerData?.triggerSmsList?.includes(itemData?.corpusName)"
                class="tw-ml-[4px] tw-shrink-0 sms-icon"
                :size="16"
                color="#165DFF"
              >
                <SvgIcon name="trigger-sms"/>
              </el-icon>
              <el-icon
                v-if="itemData?.corpusName && corpusTriggerData?.tirggerHumanList?.includes(itemData?.corpusName)"
                class="tw-ml-[4px] tw-shrink-0 human-icon"
                :size="16"
                color="#165DFF"
              >
               <SvgIcon name="trigger-human"/>
              </el-icon>
            </div>
            <!-- 机器人(type: 0)\坐席(type: 3, 4)气泡内容 -->
            <div class="box-normal box-light box-right" @click="handleAudioPlay(itemData)" v-html="formatContent(itemData, 'white')" />
          </span>
          <!-- 机器人(type: 0)\坐席(type: 3, 4)头像 -->
          <span class="dialog-o">{{itemData.type && [3, 4].includes(itemData.type) ? '坐席': (itemData?.hitIntention ? itemData?.hitIntention[0] : 'AI')}}</span>
        </div>
      </template>
    </div>
  </el-scrollbar>
  <el-empty v-else></el-empty>
</template>

<script lang="ts" setup>
import { ref, watch, onUnmounted, nextTick } from 'vue'
import { ElMessage, dayjs, } from 'element-plus'
import { InfoQueryItem, } from '@/type/speech-craft'
import { RecordDialogueData } from '@/type/task'
import { onBeforeRouteLeave } from 'vue-router'
import { changeAudioUrlOrigin, findValueInEnum } from '@/utils/utils'
import { translateBranchMsg, translateBranchType, translateHitSemanticFromBranch, getTimeClass, formatContent } from './constant'
import { TaskCallRecordItem, HangupEnum, } from '@/type/task'
import to from 'await-to-js'
import { scriptInfoModel, scriptCorpusModel } from '@/api/speech-craft'

// 组件入参props
const props = defineProps<{
  needUpdate: boolean,
  callRecordData: TaskCallRecordItem,
  dataList: RecordDialogueData[], // 人机对话信息
  clearAudio: boolean, // 对于外部调用组件时，一些操作需要清空当前播放的对话音频
}>()

// emit
const emits = defineEmits([
  'update:clearAudio', 'play', 'update:needUpdate'
])

// 对话数据
const rowDialogData = ref<Partial<RecordDialogueData>[] | null>([])

// 话术数据
// 信息查询数据
const infoQueryMap = ref<Map<string, InfoQueryItem>>(new Map([]))
const corpusTriggerData = ref<{
  tirggerHumanList?: string[],
  triggerSmsList?: string[],
}>({
  tirggerHumanList: [],
  triggerSmsList: [],
})

// 无限滚动
const count = ref(16) // 默认只展示16个对话，多得用无线滚动展开
const infiniteScrollbarRef = ref()

/** 用户音频播放模块 */
const audiosListRef = ref<HTMLAudioElement|null>(new Audio())
const currentVoice = ref<[number, number]>([-1, 0]) // 记录当前播放用户说话信息，[id, 音频urls的index]
// 播放用户说话
const handleAudioPlay = (item: Partial<RecordDialogueData>) => {
  if (item.hitBranch?.includes('沉默')) return
  if (!item.urls || item.urls.length < 1) {
    return ElMessage({
      message: '待播放链接为空',
      type: 'warning'
    })
  } else {
    !audiosListRef.value && (audiosListRef.value = new Audio())
  }
  if (item.id == currentVoice.value[0]) {
    if(audiosListRef.value && !!audiosListRef.value.paused) {
      audiosListRef.value?.play()
      audiosListRef.value.addEventListener('ended', function () {
        playList(item.urls || [], currentVoice.value[1] + 1);
      }, false)
    } else {
      audiosListRef.value?.pause()
    }
    return
  } else {
    currentVoice.value[0] = item.id!
    playList(item.urls)
    emits('play')
    return
  }
}
// 由于用户说话是一个列表，需要一次播放一个列表
const playList = (urls: string[], i: number=0) => {
  if (!urls || i >= urls.length) {
    if (audiosListRef.value) {
      audiosListRef.value.src = ''
      audiosListRef.value.pause()
      currentVoice.value = [-1, 0]
    }
    return
  }
  if (audiosListRef.value) {
    audiosListRef.value.src = changeAudioUrlOrigin(urls[i])
    currentVoice.value[1] = i
    audiosListRef.value.loop = false
    audiosListRef.value.addEventListener('loadedmetadata', () => {
      audiosListRef.value && audiosListRef.value.play();
    })
    audiosListRef.value.addEventListener('ended', function () {
      // console.warn('ended')
      emits('update:clearAudio', true)
      playList(urls, i + 1);
    }, false)
  }
}

// 清空当前播放的用户说话
const clearAudioAction = () => {
  if (audiosListRef.value) {
    audiosListRef.value.pause()
    audiosListRef.value.src = ''
  }
  audiosListRef.value = null
  emits('update:clearAudio', false)
}

// 点击跳转至转人工弹窗
const loading = ref(false)
const goPopWinTime = async () => {
  const popWinIndex = rowDialogData.value?.findIndex(item => typeof item.id !== 'number' && item.dialogTime && item.content?.includes('转人工')) || -1
  if (popWinIndex === -1) {
    ElMessage.warning('未触发转人工')
    return
  }
  loading.value = true
  count.value = Math.max(rowDialogData.value?.length || 16, popWinIndex + 16)
  await nextTick()
  const dom = document.querySelector('.pop-win-time-box')
  if (dom) {
    dom.scrollIntoView({
      block: 'start'
    })
  } else {
    ElMessage.warning('未触发转人工弹窗')
  }
  loading.value = false
}

// 当前滚动到的语料
const corpusIndex = ref<[1 | 2, number]>([1, 0])
/**
 * 调整转人工/触发短信的语料
 * @param type 1 短信 2 人工
 */
const goTriggerCorpus = (type: 1 | 2) => {
  // 如切换语料类型跳转，重新计算数量
  if (corpusIndex.value[0] !== type) {
    corpusIndex.value = [type, 0]
  }
  if (type === 1) {
    const doms = document.querySelectorAll('.sms-icon')
    if (doms[corpusIndex.value[1]]) {
      doms[corpusIndex.value[1]]?.scrollIntoView({
        block: 'start'
      })
    } else {
      ElMessage.warning(corpusIndex.value[1] === 0 ? '未找到发短信' : '未找到更多的发短信')
    }
  }
  if (type === 2) {
    const doms = document.querySelectorAll('.human-icon')
    if (doms[corpusIndex.value[1]]) {
      doms[corpusIndex.value[1]]?.scrollIntoView({
        block: 'start'
      })
    } else {
      ElMessage.warning(corpusIndex.value[1] === 0 ? '未找到转人工' : '未找到更多的转人工')
    }
  }
  corpusIndex.value[1]++
}


// 无限滚动出发函数
const handleInfiniteScroll = () => {
  count.value = Math.min(rowDialogData.value?.length || 16, count.value + 2)
}

const getScriptInfo = async () => {
  if (props.callRecordData?.speechCraftId) {
    const [err1, res1] = await to(scriptInfoModel.findInfoQueryList({id: props.callRecordData?.speechCraftId!})) as [any, InfoQueryItem[]]
    infoQueryMap.value = new Map([])
    res1?.map(item => {
      infoQueryMap.value.set(item.infoFieldName, item)
    })
    const [err2, res2] = await to(scriptCorpusModel.findTriggerCorpusByScriptId({scriptId: props.callRecordData.speechCraftId}))
    corpusTriggerData.value = res2 || {
      tirggerHumanList: [],
      triggerSmsList: [],
    }
  }
}


// 对话显示信息列表，会根据时间插入对话中，例：2021-02-02 10:12:55 电话接通
const gernateStartEndInfo = () => {
  const res = [] as {dialogTime: string, content: string}[]
  if (props.callRecordData?.talkTimeStart) {
    res.push({ dialogTime: props.callRecordData?.talkTimeStart, content: '电话接通'})
  }
  if (props.callRecordData?.startPopWinTime) {
    res.push({ dialogTime: props.callRecordData?.startPopWinTime, content: '触发转人工弹窗'})
  }
  if (props.callRecordData?.startMonitorTime) {
    res.push({ dialogTime: props.callRecordData?.startMonitorTime, content: '坐席开始监听'})
  }
  if (props.callRecordData?.endMonitorTime) {
    res.push({ dialogTime: props.callRecordData?.endMonitorTime, content: '坐席结束监听'})
  }
  if (props.callRecordData?.startAnswerTime) {
    res.push({ dialogTime: props.callRecordData?.startAnswerTime, content: '坐席开始接听'})
  }
  if (props.callRecordData?.endAnswerTime) {
    res.push({ dialogTime: props.callRecordData?.endAnswerTime, content: '坐席结束接听'})
  }
  if (props.callRecordData?.talkTimeEnd) {
    res.push({ dialogTime: props.callRecordData?.talkTimeEnd, content: (findValueInEnum(props.callRecordData?.whoHangup, HangupEnum) || '')+ '挂断通话'})
  }
  res?.sort((a, b) => {
    return dayjs(a.dialogTime).isBefore(dayjs(b.dialogTime)) ? -1 : 1
  }) || []
  return res
}


/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props.needUpdate, async () => {
  if (!props.needUpdate) return
  // 通常情况下，
  // 哪个时间早，就提取哪个；
  // 如果时间相同，则先取时间线，取完了再取对话。

  // 但是有例外的特殊情况需要处理，此时忽略上述通常情况的规则：
  // 时间线无效值：按原来的顺序放到整个通话记录的最后面；
  // 触发转人工：要放在当前相同时间的最后面，也就是同一秒内，其他时间线在前，对话在中，触发转人工时间线在后；
  // 挂断通话：任何情况下，都应该放在整个通话记录的最后面作为结束标记。

  // 通话记录
  rowDialogData.value = []
  corpusIndex.value = [1, 0] // 重置查看转人工、发短信的滚动索引

  loading.value = false
  count.value = 16
  // 获取话术数据
  getScriptInfo()
  // 时间线列表按时间升序排序，从旧到新
  const arr = gernateStartEndInfo()

  // 时间线队列索引，队头位置
  let timelineIndex = 0
  // 对话队列索引，队头位置
  let dialogIndex = 0

  const timelineLength = arr?.length ?? 0
  const dialogLength = props.dataList?.length ?? 0
  // 开始遍历
  for (let i = 0; i < dialogLength + timelineLength; i++) {
    // 对话当前元素（队头）
    const dialogHead = props.dataList[dialogIndex]
    // 时间线队列取完了，对话队列剩下的按原有顺序全部提取并放入
    if (timelineIndex >= timelineLength) {
      dialogHead && rowDialogData.value!.push(dialogHead)
      dialogIndex++
      continue
    }

    // 时间线当前元素（队头）
    const timelineHead = arr[timelineIndex]
    // 对话队列取完了，时间线队列剩下的按原有顺序全部提取并放入
    if (dialogIndex >= dialogLength) {
      timelineHead && rowDialogData.value!.push(timelineHead)
      timelineIndex++
      continue
    }

    // dayjs()的参数里null、空字符串是无效日期，做比较时始终为false
    // dayjs()的参数里undefined、日期时间字符串是有效日期，做比较时按比较结果返回true或false

    if (dialogHead?.dialogTime) {
      // 对话有时间
      const dialogDayjs = dayjs(dialogHead?.dialogTime)
      const timelineDayjs = dayjs(timelineHead?.dialogTime)
      if (dialogDayjs.isBefore(timelineDayjs)) {
        // 对话时间 早于 时间线时间，取 对话
        rowDialogData.value!.push(dialogHead)
        dialogIndex++
      } else if (dialogDayjs.isAfter(timelineDayjs)) {
        // 对话时间 晚于 时间线时间，取 时间线
        timelineHead && rowDialogData.value!.push(timelineHead)
        timelineIndex++
      } else if (dialogDayjs.isSame(timelineDayjs)) {
        // 对话时间 等于 时间线时间，优先处理特殊情况，取 对话；处理完再按通常情况，取 时间线
        if (/触发转人工|挂断通话/.test(timelineHead?.content || '')) {
          rowDialogData.value!.push(dialogHead)
          dialogIndex++
        } else {
          timelineHead && rowDialogData.value!.push(timelineHead)
          timelineIndex++
        }
      }
    } else {
      // 对话没有时间
      dialogHead && rowDialogData.value!.push(dialogHead)
      dialogIndex++
    }
  }
  audiosListRef.value = new Audio()
  await nextTick()
  // 内容更新后，滚动置顶
  infiniteScrollbarRef.value?.scrollTo({top: 0})
  emits('update:needUpdate', false)
}, {immediate: true})


watch(() => props.clearAudio, n => {
  if (n) {
    clearAudioAction()
  }
})

onUnmounted(() => {
  clearAudioAction()
  rowDialogData.value = null
  infiniteScrollbarRef.value = null
})

onBeforeRouteLeave(() => {
  clearAudioAction()
  rowDialogData.value = null
  infiniteScrollbarRef.value = null
})
</script>

<style lang="postcss" scoped>
.dialog-o {
  flex: none;
  width: 40px;
  height: 40px;
  font-size: 18px;
  line-height: 40px;
  margin-top: 16px;
  border-radius: 4px;
  color: #fff;
  background-color: var(--el-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.grey-o {
  background-color: #e6e6e6;
  color: var(--el-color-primary);
}
.time-box {
  border-radius: 4px;
  background-color: #C8C9CC;
  color: #fff;
  display: flex;
  justify-content: space-around;
  width: 240px;
  padding: 2px 6px;
  line-height: 20px;
  align-self: center;
  font-size: 13px;
  margin-top: 12px;
  margin-bottom: 12px;
}
.box-normal {
  text-align: justify;
  line-height: 22px;
  max-width: 28vw;
  border-radius: 4px;
  margin-top: 8px;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
.box-right {
  align-self: end;
  &::before {
    content: "";
    position: absolute;
    top: 7px;
    right: -5px;
    width: 0;
    height: 0;
  }
}
.box-left {
  &::before {
    content: "";
    position: absolute;
    top: 7px;
    left: -5px;
    width: 0;
    height: 0;
  }
}
.box-dark {
  border: 1px #ddd solid;
  background-color: var(--el-color-primary);
  color: #fff;
  &::before {
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }
  &.box-left {
    &::before {
      border-right: 6px solid var(--el-color-primary);
    }
  }
  &.box-right {
    &::before {
      border-left: 6px solid var(--el-color-primary);
    }
  }
}
.box-light {
  background-color: #fff;
  color: #323233;
  &::before {
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }
  &.box-left {
    &::before {
      border-right: 6px solid #fff;
    }
  }
  &.box-right {
    &::before {
      border-left: 6px solid #fff;
    }
  }
}
.box-silence {
  background-color: #e6e6e6;
  color: #323233;
  cursor: auto;
  &::before {
    content: "";
    width: 0px;
    height: 0px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 6px solid #e6e6e6;
    position: absolute;
    top: 7px;
    left: -6px;
  }
}
</style>
